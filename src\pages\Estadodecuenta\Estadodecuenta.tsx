import React, { useState, useRef, useEffect } from 'react';
import { FA6 } from '@/config';
import { PageMetadata } from '@/hoc/withPageMetadata';
import { PERMISSIONS } from '@/config/authConfig';
import { useColaboradorData } from './estadodecuenta.ts';
import { useDatosPersonales } from '@/hooks';
import { useAnimation } from '@/context/AnimationContext';
import { useUserStatusCheck } from '@/hooks';
import { CuentaInactiva } from '@/pages/Auth';

interface EyeToggleIconProps {
  isExpanded: boolean;
  onClick?: () => void;
}

interface CompanyNameDisplayProps {
  companyName: string;
  isMobile?: boolean;
}

const CompanyNameDisplay: React.FC<CompanyNameDisplayProps> = ({
  companyName,
  isMobile = false
}) => {
  const [showTooltip, setShowTooltip] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const nameRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const checkOverflow = () => {
      if (nameRef.current) {
        const element = nameRef.current;
        const isOverflowingNow = element.scrollHeight > element.clientHeight ||
                                 element.scrollWidth > element.clientWidth;
        setIsOverflowing(isOverflowingNow);
      }
    };

    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => window.removeEventListener('resize', checkOverflow);
  }, [companyName]);

  const containerClass = isMobile ? 'company-name-mobile-container' : 'company-name-container';
  const shouldShowTooltip = isOverflowing || companyName.length > (isMobile ? 30 : 40);

  return (
    <div
      className={containerClass}
      ref={nameRef}
      title={shouldShowTooltip ? companyName : undefined}
      style={{
        lineHeight: isMobile ? '1.3' : '1.4',
        maxHeight: isMobile ? '3.9em' : '2.8em',
        overflow: 'hidden',
        display: '-webkit-box',
        WebkitLineClamp: isMobile ? 3 : 2,
        WebkitBoxOrient: 'vertical',
        wordBreak: 'break-word',
        hyphens: 'auto',
        marginBottom: isMobile ? 'calc(0.25rem + 0.1vw)' : undefined,
        position: 'relative'
      }}
      onMouseEnter={() => shouldShowTooltip && setShowTooltip(true)}
      onMouseLeave={() => setShowTooltip(false)}
      onTouchStart={() => shouldShowTooltip && setShowTooltip(true)}
      onTouchEnd={() => setTimeout(() => setShowTooltip(false), 2000)}
    >
      <h6 className="fw-bold text-dark mb-0" style={{
        fontSize: isMobile ? 'calc(0.9rem + 0.2vw)' : '0.95rem',
        lineHeight: isMobile ? '1.3' : '1.4',
        margin: isMobile ? 0 : '0 0 0.25rem 0',
        fontWeight: 600,
        letterSpacing: '-0.01em',
        color: '#1a1a1a'
      }}>
        {companyName}
      </h6>

      {showTooltip && shouldShowTooltip && (
        <div
          className="position-absolute bg-dark text-white rounded px-3 py-2 shadow-lg"
          style={{
            bottom: '100%',
            left: '0',
            right: '0',
            fontSize: '0.85rem',
            fontWeight: 500,
            lineHeight: '1.3',
            zIndex: 1000,
            whiteSpace: 'normal',
            wordWrap: 'break-word',
            marginBottom: '0.5rem',
            animation: 'tooltipFadeIn 0.2s ease-out forwards',
            pointerEvents: 'none'
          }}
        >
          {companyName}
          <div
            className="position-absolute"
            style={{
              top: '100%',
              left: '1rem',
              width: 0,
              height: 0,
              borderLeft: '6px solid transparent',
              borderRight: '6px solid transparent',
              borderTop: '6px solid #212529'
            }}
          />
        </div>
      )}
    </div>
  );
};

const EyeToggleIcon: React.FC<EyeToggleIconProps> = ({
  isExpanded,
  onClick
}) => {
  const buttonSize = 'calc(36px + 0.5vw)';
  const maxButtonSize = '48px';
  const minButtonSize = '40px';
  const iconSize = 'calc(1rem + 0.15vw)';

  return (
    <button
      type="button"
      className="btn btn-primary rounded-3 shadow-sm d-inline-flex align-items-center justify-content-center position-relative"
      style={{
        width: buttonSize,
        height: buttonSize,
        maxWidth: maxButtonSize,
        maxHeight: maxButtonSize,
        minWidth: minButtonSize,
        minHeight: minButtonSize,
        border: 'none',
        transition: 'all 0.25s ease-in-out',
        transform: isExpanded ? 'scale(1.05)' : 'scale(1)',
        boxShadow: '0 4px 15px rgba(var(--bs-primary-rgb), 0.3), 0 2px 8px rgba(var(--bs-primary-rgb), 0.2)'
      }}
      title={isExpanded ? 'Ocultar detalles' : 'Ver detalles'}
      onClick={onClick}
      onMouseEnter={(e) => {
        if (e.currentTarget) {
          e.currentTarget.style.transform = 'scale(1.1)';
          e.currentTarget.style.boxShadow = '0 6px 20px rgba(var(--bs-primary-rgb), 0.4), 0 3px 12px rgba(var(--bs-primary-rgb), 0.3)';
        }
      }}
      onMouseLeave={(e) => {
        if (e.currentTarget) {
          e.currentTarget.style.transform = isExpanded ? 'scale(1.05)' : 'scale(1)';
          e.currentTarget.style.boxShadow = '0 4px 15px rgba(var(--bs-primary-rgb), 0.3), 0 2px 8px rgba(var(--bs-primary-rgb), 0.2)';
        }
      }}
      onMouseDown={(e) => {
        if (e.currentTarget) {
          e.currentTarget.style.transform = 'scale(0.95)';
        }
      }}
      onMouseUp={(e) => {
        if (e.currentTarget) {
          setTimeout(() => {
            if (e.currentTarget) {
              e.currentTarget.style.transform = isExpanded ? 'scale(1.05)' : 'scale(1)';
            }
          }, 100);
        }
      }}
      onTouchStart={(e) => {
        if (e.currentTarget) {
          e.currentTarget.style.transform = 'scale(0.95)';
        }
      }}
      onTouchEnd={(e) => {
        if (e.currentTarget) {
          setTimeout(() => {
            if (e.currentTarget) {
              e.currentTarget.style.transform = isExpanded ? 'scale(1.05)' : 'scale(1)';
            }
          }, 100);
        }
      }}
    >
      <div
        className="d-flex align-items-center justify-content-center"
        style={{
          transition: 'transform 0.3s ease-in-out',
          transform: isExpanded ? 'rotate(180deg)' : 'rotate(0deg)'
        }}
      >
        {isExpanded ? (
          <FA6.FaEyeSlash
            className="text-white"
            style={{
              fontSize: iconSize,
              filter: 'drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))'
            }}
          />
        ) : (
          <FA6.FaEye
            className="text-white"
            style={{ fontSize: iconSize }}
          />
        )}
      </div>
    </button>
  );
};

interface TabConfig {
  id: string;
  label: string;
  data: any[];
  rawData: any[];
  expandedCompanies: Set<number>;
  setExpandedCompanies: React.Dispatch<React.SetStateAction<Set<number>>>;
  animatingCompanies: Set<number>;
  setAnimatingCompanies: React.Dispatch<React.SetStateAction<Set<number>>>;
  detailRefs: React.MutableRefObject<Map<number, HTMLElement>>;
  summaryCards: SummaryCardConfig[];
  progressConfig: ProgressConfig;
  tableConfig: TableConfig;
}

interface SummaryCardConfig {
  icon: React.ComponentType<any>;
  iconBg: string;
  iconColor: string;
  title: string;
  value: string;
}

interface ProgressConfig {
  title: string | React.ReactNode;
  percentage: number;
  label: string;
}

interface TableConfig {
  title: string;
  headers: string[];
  columns: TableColumnConfig[];
  detailTitle: string;
  detailHeaders: string[];
  emptyMessage: string;
}

interface TableColumnConfig {
  key: string;
  render: (group: any) => React.ReactNode;
  className?: string;
}

const SummaryCards: React.FC<{ cards: SummaryCardConfig[] }> = ({ cards }) => (
  <div className="row g-2 g-md-3 g-lg-4 mb-1 justify-content-center">
    {cards.map((card, index) => (
      <div key={index} className="col-4 col-md-6 col-lg-4" style={{maxWidth: '280px'}}>
        <div className="card shadow-sm rounded-3 h-100">
          <div className="card-body d-flex flex-column align-items-center justify-content-center text-center p-2 p-md-4"
               style={{minHeight: 'calc(100px + 1.5vw)', maxHeight: '180px'}}>
            <div className={`${card.iconBg} mb-1 mb-md-3 d-flex align-items-center justify-content-center rounded-circle flex-shrink-0`}
                 style={{
                   width: 'calc(32px + 1vw)',
                   height: 'calc(32px + 1vw)',
                   maxWidth: '60px',
                   maxHeight: '60px',
                   minWidth: '36px',
                   minHeight: '36px'
                 }}>
              <card.icon className={card.iconColor} style={{fontSize: 'calc(0.9rem + 0.2vw)'}} />
            </div>
            <h6 className="fw-bold mb-1 mb-md-2 text-center" style={{
              fontSize: 'calc(0.65rem + 0.1vw)',
              lineHeight: '1.2',
              minHeight: 'calc(2.4em)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}>
              {card.title}
            </h6>
            <h3 className={`mb-0 fw-bold ${card.iconColor} text-center`}
                style={{fontSize: 'calc(1rem + 0.3vw)', lineHeight: '1.1'}}>
              {card.value}
            </h3>
          </div>
        </div>
      </div>
    ))}
  </div>
);

const ProgressBar: React.FC<{ config: ProgressConfig }> = ({ config }) => (
  <div className="d-flex justify-content-center mb-2 mb-md-3">
    <div className="w-100" style={{maxWidth: '600px'}}>
      <div className="card border-0 shadow-sm rounded-3 mb-2">
        <div className="card-body p-3 p-md-4">
          <div className="d-flex flex-column flex-sm-row justify-content-between align-items-start align-items-sm-center mb-3 gap-2">
            <h6 className="fw-bold mb-0 d-flex align-items-center" style={{fontSize: 'calc(0.9rem + 0.15vw)'}}>
              <FA6.FaChartPie className="me-2 text-primary" style={{fontSize: 'calc(1rem + 0.1vw)'}} />
              <span>{config.title}</span>
            </h6>
            <span className="badge bg-primary rounded-pill px-3 py-2 flex-shrink-0"
                  style={{fontSize: 'calc(0.75rem + 0.05vw)'}}>
              {config.label}
            </span>
          </div>
          <div className="progress rounded-pill" style={{ height: 'calc(8px + 0.2vw)', maxHeight: '12px', minHeight: '8px' }}>
            <div
              className="progress-bar bg-primary rounded-pill"
              role="progressbar"
              style={{
                width: `${Math.min(config.percentage, 100).toFixed(2)}%`,
                transition: 'width 0.6s ease-in-out'
              }}
              aria-valuenow={Math.min(parseFloat(config.percentage.toFixed(2)), 100)}
              aria-valuemin={0}
              aria-valuemax={100}
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

interface MovementTableProps {
  config: TabConfig;
  iconStyles: any;
  handleToggleExpand: (companyCode: number, isCredit: boolean) => void;
  isCredit: boolean;
}

const MovementTable: React.FC<MovementTableProps> = ({ config, handleToggleExpand, isCredit }) => (
  <div className="card border-0 shadow rounded-4 overflow-hidden">
    <div className="card-header bg-gradient bg-dark py-3 px-4 border-0">
      <h5 className="fw-bold mb-0 text-white d-flex align-items-center">
        <div className="bg-primary bg-opacity-25 rounded-circle p-2 me-3">
          <FA6.FaMoneyBillTransfer className="text-white" size={16} />
        </div>
        {config.tableConfig.title}
      </h5>
    </div>
    <div className="card-body p-0">
      {/* Desktop Card View */}
      <div className="d-none d-lg-block">
        {config.data?.length === 0 ? (
          <div className="text-center py-5 px-3">
            <div className="text-muted">
              <div className="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                   style={{width: '80px', height: '80px'}}>
                <FA6.FaCircleInfo className="text-secondary" size={24} />
              </div>
              <p className="mb-0 fw-medium fs-6">{config.tableConfig.emptyMessage}</p>
            </div>
          </div>
        ) : (
          <div className="px-2 py-1 px-lg-1 px-xl-0">
            {config.data?.map((group) => (
              <div key={group.companyCode} className="mb-2">
                {/* Desktop Company Card */}
                <div className="card border-0 shadow rounded-3 overflow-hidden" style={{
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.9) 100%)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(0,0,0,0.08)',
                  maxWidth: 'fit-content',
                  minWidth: '600px',
                  width: 'auto'
                }}>
                  <div className="card-body p-2" style={{padding: '0.75rem'}}>
                    <div className="d-flex align-items-center justify-content-between mb-2">
                      <div className="d-flex align-items-center flex-grow-1 me-3">
                        <div className="bg-primary bg-opacity-15 border border-primary border-opacity-30 rounded-3 me-2 d-flex align-items-center justify-content-center flex-shrink-0 shadow-sm"
                             style={{
                               width: '42px',
                               height: '42px',
                               background: 'linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.15) 0%, rgba(var(--bs-primary-rgb), 0.08) 100%)'
                             }}>
                          <FA6.FaBuilding className="text-white" style={{fontSize: '0.9rem', filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))'}} />
                        </div>
                        <div className="flex-grow-1 min-width-0 me-2">
                          <CompanyNameDisplay
                            companyName={group.companyName}
                            isMobile={false}
                          />
                          <div className="d-flex align-items-center mt-1">
                            <div className="bg-secondary bg-opacity-15 rounded-pill px-2 py-1 d-flex align-items-center">
                              <FA6.FaHashtag className="text-white me-1" style={{fontSize: '0.5rem'}} />
                              <small className="text-white fw-semibold" style={{
                                fontSize: '0.65rem',
                                letterSpacing: '0.02em'
                              }}>
                                {group.companyCode}
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>

                      <div className="flex-shrink-0 ms-1">
                        <div
                          onClick={() => handleToggleExpand(group.companyCode, isCredit)}
                          role="button"
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              handleToggleExpand(group.companyCode, isCredit);
                            }
                          }}
                        >
                          <EyeToggleIcon isExpanded={config.expandedCompanies.has(group.companyCode)} />
                        </div>
                      </div>
                    </div>

                    {/* Desktop Summary Grid */}
                    <div className="d-flex gap-2 mb-0 justify-content-start">
                      {config.tableConfig.columns.map((column, index) => (
                        <div key={index} className="flex-shrink-0" style={{minWidth: '120px', maxWidth: '140px'}}>
                          <div className="text-center p-2 bg-white bg-opacity-70 rounded-2 shadow-sm border border-light border-opacity-50" style={{
                            backdropFilter: 'blur(4px)'
                          }}>
                            <div className="small text-muted fw-semibold mb-1 d-flex align-items-center justify-content-center" style={{
                              fontSize: '0.65rem',
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px'
                            }}>
                              {index === 0 && <FA6.FaFileInvoice className="me-1 text-white" style={{fontSize: '0.55rem'}} />}
                              {index === 1 && <FA6.FaReceipt className="me-1 text-white" style={{fontSize: '0.55rem'}} />}
                              {index === 2 && <FA6.FaCalculator className="me-1 text-white" style={{fontSize: '0.55rem'}} />}
                              <span>{config.tableConfig.headers[index + 1]}</span>
                            </div>
                            <div className="fw-bold" style={{fontSize: '0.8rem'}}>
                              {column.render(group)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Desktop Detail Expansion */}
                {config.expandedCompanies.has(group.companyCode) && (
                  <div
                    ref={(el) => {
                      if (el) config.detailRefs.current.set(group.companyCode, el);
                    }}
                    className={`detail-content ${
                      config.animatingCompanies.has(group.companyCode) ? 'detail-expand-enter' : 'detail-expand-enter-active'
                    }`}
                    style={{
                      background: 'linear-gradient(135deg, rgba(248, 249, 250, 0.98) 0%, rgba(233, 236, 239, 0.9) 100%)',
                      margin: '0.5rem 0 0 0',
                      padding: '0.5rem',
                      backdropFilter: 'blur(8px)',
                      boxShadow: 'inset 0 2px 8px rgba(0,0,0,0.06)',
                      maxWidth: 'fit-content',
                      minWidth: '600px',
                      width: 'auto',
                      borderRadius: '8px'
                    }}
                  >
                    {/* Desktop Movement Cards */}
                    <div className="d-flex flex-column gap-1">
                      {config.rawData
                        ?.filter(movement => movement.CODEMP === group.companyCode)
                        .map((movement) => (
                          <div key={movement.NROTRANS} className="w-100" style={{maxWidth: 'fit-content', minWidth: '580px'}}>
                            <div className="card border-0 shadow-sm rounded-2 overflow-hidden">
                              {/* Card Header */}
                              <div className="card-header border-0 p-0" style={{
                                background: movement.TIPO_COMPROBANTE === 'FB'
                                  ? 'linear-gradient(135deg, #007bff 0%, #0056b3 100%)'
                                  : 'linear-gradient(135deg, #28a745 0%, #1e7e34 100%)',
                                borderRadius: '0.375rem 0.375rem 0 0'
                              }}>
                                <div className="d-flex align-items-center justify-content-between py-1 px-2">
                                  {/* Left: Document Type */}
                                  <div className="d-flex align-items-center flex-shrink-0">
                                    <div className="bg-white bg-opacity-20 rounded-1 px-2 py-1 me-2">
                                      <FA6.FaFileInvoice className="text-dark me-1" style={{fontSize: '0.55rem'}} />
                                      <span className="text-dark fw-bold" style={{fontSize: '0.6rem', letterSpacing: '0.5px'}}>
                                        {movement.TIPO_COMPROBANTE === 'FB' ? 'FACTURA' : 'RECIBO'}
                                      </span>
                                    </div>
                                  </div>

                                  {/* Center: Document Number */}
                                  <div className="text-center flex-grow-1 mx-2">
                                    <div className="fw-bold text-dark bg-white bg-opacity-20 rounded-1 px-2 py-1 d-flex align-items-center justify-content-center font-monospace"
                                         style={{
                                           fontSize: '0.7rem',
                                           letterSpacing: '0.8px',
                                           minHeight: '18px',
                                           lineHeight: '1'
                                         }}>
                                      {movement.PREFIJO}-{movement.NUMERO}
                                    </div>
                                  </div>

                                  {/* Right: Date */}
                                  <div className="text-end flex-shrink-0">
                                    <div className="bg-white bg-opacity-20 rounded-1 px-2 py-1">
                                      <span className="text-dark fw-semibold" style={{fontSize: '0.6rem'}}>
                                        {new Date(movement.FECHA).toLocaleDateString('es-ES', {
                                          day: '2-digit',
                                          month: '2-digit',
                                          year: 'numeric'
                                        })}
                                      </span>
                                    </div>
                                  </div>
                                </div>
                              </div>

                              {/* Card Body - Single Row Layout */}
                              <div className="card-body p-2" style={{paddingTop: '0.5rem', paddingBottom: '0.5rem'}}>

                                {/* Single Row Layout */}
                                <div className="d-flex justify-content-between align-items-center">

                                  {/* Location Section */}
                                  <div className="d-flex align-items-center flex-grow-1 me-3">
                                    <div className="rounded-circle bg-info p-1 me-2 d-flex align-items-center justify-content-center flex-shrink-0" style={{width: '18px', height: '18px'}}>
                                      <FA6.FaLocationDot className="text-dark" style={{fontSize: '0.6rem'}} />
                                    </div>
                                    <div className="text-dark fw-semibold" style={{
                                      fontSize: '0.8rem',
                                      lineHeight: '1.2',
                                      opacity: 0.95,
                                      wordBreak: 'break-word',
                                      overflow: 'hidden',
                                      textOverflow: 'ellipsis',
                                      whiteSpace: 'nowrap'
                                    }}>
                                      {movement.NOMBRE_SUCURSAL}
                                    </div>
                                  </div>

                                  {/* Amount Section */}
                                  <div className="d-flex align-items-center flex-shrink-0">
                                    <div className={`rounded-circle p-1 me-2 d-flex align-items-center justify-content-center ${
                                      movement.TIPO_COMPROBANTE === 'FB' ? 'bg-danger' : 'bg-success'
                                    }`} style={{width: '18px', height: '18px'}}>
                                      <FA6.FaDollarSign className="text-dark" style={{fontSize: '0.6rem'}} />
                                    </div>
                                    <div className={`fw-bold d-flex align-items-center ${
                                      movement.TIPO_COMPROBANTE === 'FB' ? 'text-danger' : 'text-success'
                                    }`} style={{
                                      fontSize: '0.9rem',
                                      lineHeight: '1.2',
                                      fontFamily: 'system-ui, -apple-system, sans-serif',
                                      textShadow: '0 1px 2px rgba(0,0,0,0.05)'
                                    }}>
                                      <span className="me-1">$</span>
                                      <span>{parseFloat(movement.TOTAL_EN_DOLARES).toFixed(2)}</span>
                                    </div>
                                  </div>

                                </div>
                              </div>
                            </div>
                          </div>
                        ))}
                    </div>
                  </div>
                )}

              </div>
            ))}
          </div>
        )}
      </div>

      {/* Mobile Card View */}
      <div className="d-lg-none">
        {config.data?.length === 0 ? (
          <div className="text-center py-5 px-3">
            <div className="text-muted">
              <div className="bg-light rounded-circle d-inline-flex align-items-center justify-content-center mb-3"
                   style={{width: 'calc(15vw + 45px)', height: 'calc(15vw + 45px)', maxWidth: '80px', maxHeight: '80px'}}>
                <FA6.FaCircleInfo className="text-secondary" style={{fontSize: 'calc(1rem + 0.5vw)'}} />
              </div>
              <p className="mb-0 fw-medium fs-6">{config.tableConfig.emptyMessage}</p>
            </div>
          </div>
        ) : (
          <div className="px-2 py-1">
            {config.data?.map((group) => (
              <div key={group.companyCode} className="mb-3">
                {/* Mobile Company Card */}
                <div className="card border-0 shadow rounded-4 overflow-hidden" style={{
                  background: 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.9) 100%)',
                  backdropFilter: 'blur(10px)',
                  border: '1px solid rgba(0,0,0,0.08)'
                }}>
                  <div className="card-body p-3 p-md-4">
                    <div className="d-flex align-items-start justify-content-between mb-3 mb-md-4">
                      <div className="d-flex align-items-start flex-grow-1 me-4">
                        <div className="bg-primary bg-opacity-15 border border-primary border-opacity-30 rounded-4 me-3 d-flex align-items-center justify-content-center flex-shrink-0 shadow-sm"
                             style={{
                               width: 'calc(12vw + 36px)',
                               height: 'calc(12vw + 36px)',
                               maxWidth: '68px',
                               maxHeight: '68px',
                               minWidth: '52px',
                               minHeight: '52px',
                               background: 'linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.15) 0%, rgba(var(--bs-primary-rgb), 0.08) 100%)'
                             }}>
                          <FA6.FaBuilding className="text-white" style={{fontSize: 'calc(1.2rem + 0.3vw)', filter: 'drop-shadow(0 1px 2px rgba(0,0,0,0.1))'}} />
                        </div>
                        <div className="flex-grow-1 min-width-0 me-2">
                          <CompanyNameDisplay
                            companyName={group.companyName}
                            isMobile={true}
                          />
                          <div className="d-flex align-items-center mt-1">
                            <div className="bg-secondary bg-opacity-15 rounded-pill px-2 py-1 d-flex align-items-center">
                              <FA6.FaHashtag className="text-white me-1" style={{fontSize: '0.6rem'}} />
                              <small className="text-white fw-semibold" style={{
                                fontSize: 'calc(0.7rem + 0.05vw)',
                                letterSpacing: '0.02em'
                              }}>
                                {group.companyCode}
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="flex-shrink-0 ms-2">
                        <div
                          onClick={() => handleToggleExpand(group.companyCode, isCredit)}
                          role="button"
                          tabIndex={0}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' || e.key === ' ') {
                              e.preventDefault();
                              handleToggleExpand(group.companyCode, isCredit);
                            }
                          }}
                        >
                          <EyeToggleIcon isExpanded={config.expandedCompanies.has(group.companyCode)} />
                        </div>
                      </div>
                    </div>

                    {/* Mobile Summary Grid */}
                    <div className="row g-2 mb-0">
                      {config.tableConfig.columns.map((column, index) => (
                        <div key={index} className="col-4">
                          <div className="text-center p-2 bg-white bg-opacity-70 rounded-3 shadow-sm border border-light border-opacity-50" style={{
                            backdropFilter: 'blur(4px)'
                          }}>
                            <div className="small text-muted fw-semibold mb-1 d-flex align-items-center justify-content-center" style={{
                              fontSize: 'calc(0.68rem + 0.08vw)',
                              textTransform: 'uppercase',
                              letterSpacing: '0.5px'
                            }}>
                              {index === 0 && <FA6.FaFileInvoice className="me-1 text-white" style={{fontSize: '0.6rem'}} />}
                              {index === 1 && <FA6.FaReceipt className="me-1 text-white" style={{fontSize: '0.6rem'}} />}
                              {index === 2 && <FA6.FaCalculator className="me-1 text-white" style={{fontSize: '0.6rem'}} />}
                              <span>{config.tableConfig.headers[index + 1]}</span>
                            </div>
                            <div className="fw-bold" style={{fontSize: 'calc(0.82rem + 0.12vw)'}}>
                              {column.render(group)}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Mobile Detail Expansion */}
                {config.expandedCompanies.has(group.companyCode) && (
                  <div
                    ref={(el) => {
                      if (el) config.detailRefs.current.set(group.companyCode, el);
                    }}
                    className={`detail-content mt-2 ${
                      config.animatingCompanies.has(group.companyCode) ? 'detail-expand-enter' : 'detail-expand-enter-active'
                    }`}
                    style={{
                      background: 'linear-gradient(135deg, rgba(248, 249, 250, 0.98) 0%, rgba(233, 236, 239, 0.9) 100%)',
                      borderRadius: '12px',
                      border: '1px solid rgba(108, 117, 125, 0.12)',
                      margin: '0.5rem 0 0 0',
                      padding: '0',
                      backdropFilter: 'blur(8px)',
                      boxShadow: '0 4px 12px rgba(0,0,0,0.08), 0 2px 6px rgba(0,0,0,0.04)'
                    }}
                  >
                    <div className="p-3 p-md-4">
                      <div className="d-flex align-items-center mb-3 pb-2 border-bottom border-light border-opacity-50">
                        <div className="bg-primary bg-opacity-15 rounded-circle p-2 me-3 shadow-sm">
                          <FA6.FaListUl className="text-white" style={{fontSize: '0.8rem'}} />
                        </div>
                        <h6 className="fw-bold mb-0 text-dark" style={{fontSize: '0.9rem', lineHeight: '1.2'}}>
                          Detalle de Movimientos
                        </h6>
                      </div>

                      <div className="row g-2">
                        {config.rawData
                          ?.filter(movement => movement.CODEMP === group.companyCode)
                          .map((movement, index) => (
                            <div key={movement.NROTRANS} className="col-12">
                              <div className={`card border rounded-4 shadow-sm overflow-hidden ${
                                index % 2 === 0
                                  ? 'bg-white border-primary border-opacity-20'
                                  : 'bg-light bg-opacity-90 border-secondary border-opacity-25'
                              }`}
                                   style={{
                                     transition: 'all 0.25s ease-in-out',
                                     borderWidth: '1.5px',
                                     backdropFilter: 'blur(4px)',
                                     background: index % 2 === 0
                                       ? 'linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.8) 100%)'
                                       : 'linear-gradient(135deg, rgba(248,249,250,0.9) 0%, rgba(233,236,239,0.7) 100%)'
                                   }}>

                                {/* Invoice Header */}
                                <div className={`card-header py-2 px-3 border-0 position-relative ${
                                  movement.TIPO_COMPROBANTE === 'FB' ? 'bg-primary bg-opacity-8' :
                                  movement.TIPO_COMPROBANTE === 'RC' ? 'bg-success bg-opacity-8' :
                                  movement.TIPO_COMPROBANTE === 'CB' ? 'bg-success bg-opacity-8' :
                                  'bg-info bg-opacity-8'
                                }`} style={{
                                  borderBottom: '1px solid rgba(0,0,0,0.06)',
                                  background: movement.TIPO_COMPROBANTE === 'FB'
                                    ? 'linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.08) 0%, rgba(var(--bs-primary-rgb), 0.04) 100%)'
                                    : movement.TIPO_COMPROBANTE === 'RC' || movement.TIPO_COMPROBANTE === 'CB'
                                    ? 'linear-gradient(135deg, rgba(var(--bs-success-rgb), 0.08) 0%, rgba(var(--bs-success-rgb), 0.04) 100%)'
                                    : 'linear-gradient(135deg, rgba(var(--bs-info-rgb), 0.08) 0%, rgba(var(--bs-info-rgb), 0.04) 100%)'
                                }}>
                                  <div className="d-flex justify-content-between align-items-center">

                                    {/* Left: Document Type */}
                                    <div className="d-flex align-items-center flex-shrink-0">
                                      <div className={`me-2 rounded-3 p-1 d-flex align-items-center justify-content-center shadow-sm ${
                                        movement.TIPO_COMPROBANTE === 'FB' ? 'bg-primary border border-primary border-opacity-25' :
                                        movement.TIPO_COMPROBANTE === 'RC' ? 'bg-success border border-success border-opacity-25' :
                                        movement.TIPO_COMPROBANTE === 'CB' ? 'bg-success border border-success border-opacity-25' :
                                        'bg-info border border-info border-opacity-25'
                                      }`} style={{width: '26px', height: '26px'}}>
                                        {movement.TIPO_COMPROBANTE === 'FB' ? (
                                          <FA6.FaFileInvoiceDollar className="text-white" style={{fontSize: '0.75rem'}} />
                                        ) : movement.TIPO_COMPROBANTE === 'RC' ? (
                                          <FA6.FaReceipt className="text-white" style={{fontSize: '0.75rem'}} />
                                        ) : movement.TIPO_COMPROBANTE === 'CB' ? (
                                          <FA6.FaFileContract className="text-white" style={{fontSize: '0.75rem'}} />
                                        ) : (
                                          <FA6.FaFile className="text-white" style={{fontSize: '0.75rem'}} />
                                        )}
                                      </div>
                                      <span className={`badge rounded-pill px-2 py-1 fw-bold shadow-sm d-flex align-items-center justify-content-center ${
                                        movement.TIPO_COMPROBANTE === 'FB' ? 'bg-primary text-white' :
                                        movement.TIPO_COMPROBANTE === 'RC' ? 'bg-success text-white' :
                                        movement.TIPO_COMPROBANTE === 'CB' ? 'bg-success text-white' :
                                        'bg-info text-white'
                                      }`} style={{
                                        fontSize: '0.65rem',
                                        letterSpacing: '0.03em',
                                        textShadow: '0 1px 2px rgba(0,0,0,0.1)',
                                        minHeight: '22px',
                                        lineHeight: '1'
                                      }}>
                                        {movement.TIPO_COMPROBANTE === 'FB' ? (isCredit ? 'FACTURA' : 'COMPRA') :
                                         movement.TIPO_COMPROBANTE === 'CB' ? (isCredit ? 'COMPROBANTE' : 'DEVOLUCIÓN') :
                                         movement.TIPO_COMPROBANTE === 'RC' ? (isCredit ? 'RECIBO' : 'PAGO') : movement.TIPO_COMPROBANTE}
                                      </span>
                                    </div>

                                    {/* Center: Document Number */}
                                    <div className="text-center flex-grow-1 mx-3">
                                      <div className="fw-bold text-dark font-monospace bg-white bg-opacity-60 rounded-2 px-2 py-1 shadow-sm border border-dark border-opacity-10 d-flex align-items-center justify-content-center"
                                           style={{
                                             fontSize: '0.85rem',
                                             letterSpacing: '0.8px',
                                             backdropFilter: 'blur(4px)',
                                             minHeight: '24px',
                                             lineHeight: '1'
                                           }}>
                                        {movement.PREFIJO}-{movement.NUMERO}
                                      </div>
                                    </div>

                                    {/* Right: Date */}
                                    <div className="text-end flex-shrink-0">
                                      <div className="fw-semibold text-dark bg-white bg-opacity-40 rounded-2 px-2 py-1 d-flex align-items-center justify-content-center"
                                           style={{
                                             fontSize: '0.72rem',
                                             lineHeight: '1',
                                             backdropFilter: 'blur(2px)',
                                             minHeight: '24px'
                                           }}>
                                        {new Date(movement.FECHA).toLocaleDateString('es-ES', {
                                          day: '2-digit',
                                          month: '2-digit',
                                          year: 'numeric'
                                        })}
                                      </div>
                                    </div>

                                  </div>
                                </div>

                                {/* Invoice Body */}
                                <div className="card-body p-3" style={{paddingTop: '0.6rem', paddingBottom: '0.6rem'}}>

                                  {/* Single Row Layout */}
                                  <div className="d-flex justify-content-between align-items-center">

                                    {/* Location Section */}
                                    <div className="d-flex align-items-center flex-grow-1 me-3">
                                      <div className="rounded-circle bg-info p-1 me-2 d-flex align-items-center justify-content-center flex-shrink-0" style={{width: '18px', height: '18px'}}>
                                        <FA6.FaLocationDot className="text-white" style={{fontSize: '0.6rem'}} />
                                      </div>
                                      <div className="text-dark fw-semibold" style={{
                                        fontSize: '0.8rem',
                                        lineHeight: '1.2',
                                        opacity: 0.95,
                                        wordBreak: 'break-word',
                                        overflow: 'hidden',
                                        textOverflow: 'ellipsis',
                                        whiteSpace: 'nowrap'
                                      }}>
                                        {movement.NOMBRE_SUCURSAL}
                                      </div>
                                    </div>

                                    {/* Amount Section */}
                                    <div className="d-flex align-items-center flex-shrink-0">
                                      <div className={`rounded-circle p-1 me-2 d-flex align-items-center justify-content-center ${
                                        movement.TIPO_COMPROBANTE === 'FB' ? 'bg-danger' : 'bg-success'
                                      }`} style={{width: '18px', height: '18px'}}>
                                        <FA6.FaDollarSign className="text-white" style={{fontSize: '0.6rem'}} />
                                      </div>
                                      <div className={`fw-bold d-flex align-items-center ${
                                        movement.TIPO_COMPROBANTE === 'FB' ? 'text-danger' : 'text-success'
                                      }`} style={{
                                        fontSize: '0.9rem',
                                        lineHeight: '1.2',
                                        fontFamily: 'system-ui, -apple-system, sans-serif',
                                        textShadow: '0 1px 2px rgba(0,0,0,0.05)'
                                      }}>
                                        <span className="me-1">$</span>
                                        <span>{parseFloat(movement.TOTAL_EN_DOLARES).toFixed(2)}</span>
                                      </div>
                                    </div>

                                  </div>
                                </div>
                              </div>
                            </div>
                          ))}
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  </div>
);

const EstadoDeCuenta: React.FC & { pageMetadata: PageMetadata } = () => {
  const datosPersonales = useDatosPersonales();
  const { animateIn, durations, classes, smoothScrollTo, timingFunctions } = useAnimation();
  const { status: userStatus, loading: statusLoading, checkStatus } = useUserStatusCheck();

  useEffect(() => {
    const refreshData = async () => {
      try {
        await datosPersonales.refetchDatosPersonales();
      } catch (error) {
        console.error('[EstadoDeCuenta] Error refreshing personal data:', error);
      }
    };

    refreshData();
  }, []);

  const {
    rawMovements,
    processedMovements,
    rawCashMovements,
    processedCashMovements,
    currentMonthCashTotals,
    loading,
    error
  } = useColaboradorData();

  const [expandedCompanies, setExpandedCompanies] = useState<Set<number>>(new Set());
  const [expandedCashCompanies, setExpandedCashCompanies] = useState<Set<number>>(new Set());
  const [activeTab, setActiveTab] = useState('credito');
  const [animatingCompanies, setAnimatingCompanies] = useState<Set<number>>(new Set());
  const [animatingCashCompanies, setAnimatingCashCompanies] = useState<Set<number>>(new Set());
  const [isVerifying, setIsVerifying] = useState(false);

  const detailRefs = useRef<Map<number, HTMLElement>>(new Map());
  const cashDetailRefs = useRef<Map<number, HTMLElement>>(new Map());
  const totalUsedCredit = processedMovements?.reduce((total, group) => total + group.balance, 0) || 0;

  const scrollToElement = (element: HTMLElement) => {
    const performScroll = () => {
      const isMobile = window.innerWidth < 768;

      if (isMobile) {
        smoothScrollTo(element, {
          offset: 10,
          duration: durations.scroll,
          easing: timingFunctions.easeOutCubic
        });
        return;
      }

      let table = element.closest('table');
      if (!table) {
        const container = element.closest('.table-responsive');
        table = container?.querySelector('table') || null;
      }
      if (!table) {
        const activeTab = document.querySelector('.tab-pane.active');
        const tableContainer = activeTab?.querySelector('.table-responsive table');
        table = tableContainer as HTMLTableElement;
      }

      const tableHeader = table?.querySelector('thead');
      if (tableHeader) {
        const headerRect = tableHeader.getBoundingClientRect();
        const distance = Math.abs(headerRect.top);
        let scrollDuration = durations.scroll;
        let scrollEasing = timingFunctions.easeOutCubic;

        if (distance > 800) {
          scrollDuration = durations.scrollSlow;
          scrollEasing = timingFunctions.easeInOutQuad;
        } else if (distance > 400) {
          scrollDuration = durations.scroll;
          scrollEasing = timingFunctions.easeOutCubic;
        } else {
          scrollDuration = Math.max(durations.scroll * 0.7, 600);
          scrollEasing = timingFunctions.easeOutCubic;
        }

        smoothScrollTo(tableHeader, {
          offset: 20,
          duration: scrollDuration,
          easing: scrollEasing
        });
      } else {
        smoothScrollTo(element, {
          offset: 20,
          duration: durations.scroll,
          easing: timingFunctions.easeOutCubic
        });
      }
    };

    requestAnimationFrame(() => {
      setTimeout(performScroll, 150);
    });
  };
  const rawCreditLimit = datosPersonales.datosPersonales?.LimiteCredito || '0';
  const creditLimit = parseFloat(String(rawCreditLimit)) || 0;

  const availableCredit = Math.max(0, creditLimit - totalUsedCredit);
  const usagePercentage = creditLimit > 0 ? (totalUsedCredit / creditLimit) * 100 : 0;
  const currentMonthName = new Date().toLocaleString('es-ES', { month: 'long' });
  const totalUsedCash = currentMonthCashTotals?.balance || 0;
  const availableCashCredit = Math.max(0, creditLimit - totalUsedCash);
  const cashUsagePercentage = creditLimit > 0 ? (totalUsedCash / creditLimit) * 100 : 0;

  const iconStyles = {
    iconCircle: {
      width: '60px',
      height: '60px',
      borderRadius: '50%',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    },
    iconSquare: {
      width: '40px',
      height: '40px',
      borderRadius: '8px',
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }
  };

  const tabConfigs: Record<string, TabConfig> = {
    credito: {
      id: 'credito',
      label: 'Crédito',
      data: processedMovements || [],
      rawData: rawMovements || [],
      expandedCompanies,
      setExpandedCompanies,
      animatingCompanies,
      setAnimatingCompanies,
      detailRefs,
      summaryCards: [
        {
          icon: FA6.FaCreditCard,
          iconBg: 'bg-primary-subtle',
          iconColor: 'text-primary',
          title: 'Limite de crédito	',
          value: `$ ${datosPersonales.limiteCredito}`
        },
        {
          icon: FA6.FaMoneyBillTransfer,
          iconBg: 'bg-warning-subtle',
          iconColor: 'text-danger',
          title: 'Utilizado',
          value: `$ ${totalUsedCredit.toFixed(2)}`
        },
        {
          icon: FA6.FaMoneyBillTrendUp,
          iconBg: 'bg-success-subtle',
          iconColor: 'text-success',
          title: 'Disponible',
          value: `$ ${availableCredit.toFixed(2)}`
        }
      ],
      progressConfig: {
        title: 'Uso de Crédito',
        percentage: usagePercentage,
        label: usagePercentage > 100 ? "100%+ Utilizado" : `${usagePercentage.toFixed(2)}% Utilizado`
      },
      tableConfig: {
        title: 'Historico de Compras',
        headers: ['Empresa', 'Facturas', 'Recibos', 'Utilizado', 'Detalles'],
        columns: [
          {
            key: 'fbTotal',
            render: (group) => <span className="fw-bold text-primary fs-6">$ {group.fbTotal.toFixed(2)}</span>,
            className: 'fw-bold text-primary'
          },
          {
            key: 'cbRcTotal',
            render: (group) => <span className="fw-bold text-success fs-6">$ {group.cbRcTotal.toFixed(2)}</span>,
            className: 'fw-bold text-success'
          },
          {
            key: 'balance',
            render: (group) => <span className="fw-bold text-danger fs-6">$ {group.balance.toFixed(2)}</span>,
            className: 'fw-bold text-danger'
          }
        ],
        detailTitle: 'Detalle de Movimientos',
        detailHeaders: ['Fecha', 'Tipo', 'Comprobante', 'Sucursal', 'Monto'],
        emptyMessage: 'No hay movimientos disponibles'
      }
    },
    contado: {
      id: 'contado',
      label: 'Contado',
      data: processedCashMovements || [],
      rawData: rawCashMovements || [],
      expandedCompanies: expandedCashCompanies,
      setExpandedCompanies: setExpandedCashCompanies,
      animatingCompanies: animatingCashCompanies,
      setAnimatingCompanies: setAnimatingCashCompanies,
      detailRefs: cashDetailRefs,
      summaryCards: [
        {
          icon: FA6.FaWallet,
          iconBg: 'bg-primary-subtle',
          iconColor: 'text-primary',
          title: 'Limite en el mes',
          value: `$ ${datosPersonales.limiteCredito}`
        },
        {
          icon: FA6.FaMoneyCheck,
          iconBg: 'bg-warning-subtle',
          iconColor: 'text-danger',
          title: 'Utilizado en el mes',
          value: `$ ${totalUsedCash.toFixed(2)}`
        },
        {
          icon: FA6.FaMoneyBillTrendUp,
          iconBg: 'bg-success-subtle',
          iconColor: 'text-success',
          title: 'Disponible en el mes',
          value: `$ ${availableCashCredit.toFixed(2)}`
        }
      ],
      progressConfig: {
        title: (
          <>
            Utilizado en el mes de{' '}
            <span className="ms-2 badge bg-warning text-dark fw-bold px-3 py-1 rounded-pill shadow-sm">
              {currentMonthName}
            </span>
          </>
        ),
        percentage: cashUsagePercentage,
        label: cashUsagePercentage > 100 ? "100%+ Utilizado" : `${cashUsagePercentage.toFixed(2)}% Utilizado`
      },
      tableConfig: {
        title: 'Historico de Compras',
        headers: ['Empresa', 'Compras', 'Detalles'],
        columns: [
          {
            key: 'balance',
            render: (group) => <span className="fw-bold text-danger fs-6">$ {group.balance.toFixed(2)}</span>,
            className: 'fw-bold text-danger'
          }
        ],
        detailTitle: 'Detalle de Movimientos de Contado',
        detailHeaders: ['Fecha', 'Tipo', 'Comprobante', 'Sucursal', 'Monto'],
        emptyMessage: 'No hay movimientos disponibles'
      }
    }
  };

  const renderTabContent = (tabId: string) => {
    const config = tabConfigs[tabId];
    if (!config) return null;

    return (
      <div className={`tab-pane p-2 p-md-3 p-lg-1 p-xl-0 ${activeTab === tabId ? 'active show' : 'd-none'}`} id={tabId} role="tabpanel">
        <div className="container-fluid px-0">
          <SummaryCards cards={config.summaryCards} />
          <ProgressBar config={config.progressConfig} />
          <MovementTable
            config={config}
            iconStyles={iconStyles}
            handleToggleExpand={handleToggleExpand}
            isCredit={tabId === 'credito'}
          />
        </div>
      </div>
    );
  };

  const handleToggleExpand = async (companyCode: number, isCredit: boolean = true) => {
    const expandedSet = isCredit ? expandedCompanies : expandedCashCompanies;
    const setExpandedSet = isCredit ? setExpandedCompanies : setExpandedCashCompanies;
    const setAnimatingSet = isCredit ? setAnimatingCompanies : setAnimatingCashCompanies;
    const refs = isCredit ? detailRefs : cashDetailRefs;

    const isExpanded = expandedSet.has(companyCode);
    const reducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

    if (!isExpanded) {
      setExpandedSet(prev => new Set(prev).add(companyCode));
      setAnimatingSet(prev => new Set(prev).add(companyCode));

      requestAnimationFrame(() => {
        const element = refs.current.get(companyCode);
        if (element) {
          if (reducedMotion) {
            element.style.opacity = '1';
            element.style.transform = 'none';
            scrollToElement(element);
            setAnimatingSet(prev => {
              const newSet = new Set(prev);
              newSet.delete(companyCode);
              return newSet;
            });
          } else {
            animateIn(element, classes.slideInDown, durations.fast).then(() => {
              scrollToElement(element);
              setAnimatingSet(prev => {
                const newSet = new Set(prev);
                newSet.delete(companyCode);
                return newSet;
              });
            });
          }
        }
      });
    } else {
      setAnimatingSet(prev => new Set(prev).add(companyCode));
      const element = refs.current.get(companyCode);

      if (element) {
        if (reducedMotion) {
          setExpandedSet(prev => {
            const newSet = new Set(prev);
            newSet.delete(companyCode);
            return newSet;
          });
          setAnimatingSet(prev => {
            const newSet = new Set(prev);
            newSet.delete(companyCode);
            return newSet;
          });
        } else {
          element.style.transition = 'all 0.25s cubic-bezier(0.4, 0, 0.2, 1)';
          element.style.opacity = '0';
          element.style.transform = 'translateY(-8px) scale(0.98)';
          element.style.maxHeight = '0px';
          element.style.overflow = 'hidden';
          element.style.paddingTop = '0';
          element.style.paddingBottom = '0';
          element.style.marginTop = '0';
          element.style.marginBottom = '0';

          setTimeout(() => {
            setExpandedSet(prev => {
              const newSet = new Set(prev);
              newSet.delete(companyCode);
              return newSet;
            });
            setAnimatingSet(prev => {
              const newSet = new Set(prev);
              newSet.delete(companyCode);
              return newSet;
            });
          }, 250);
        }
      }
    }
  };

  const handleVerifyStatus = async () => {
    if (statusLoading || isVerifying) {
      return;
    }

    setIsVerifying(true);
    try {
      await Promise.all([
        checkStatus(),
        datosPersonales.refetchDatosPersonales()
      ]);
    } catch (error) {
      console.error('[EstadoDeCuenta] Error verifying status:', error);
    } finally {
      setIsVerifying(false);
    }
  };

  useEffect(() => {
    return () => {
      detailRefs.current.clear();
      cashDetailRefs.current.clear();
    };
  }, []);



  if (loading) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-2">Cargando datos...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center text-danger">
          <FA6.FaTriangleExclamation size={32} />
          <p className="mt-2">Error: {error.message}</p>
          <button className="btn btn-primary mt-2" onClick={() => window.location.reload()}>Reintentar</button>
        </div>
      </div>
    );
  }

  if (statusLoading) {
    return (
      <div className="container d-flex justify-content-center align-items-center min-vh-50">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Verificando estado...</span>
          </div>
          <p className="mt-2">Verificando estado del usuario...</p>
        </div>
      </div>
    );
  }

  if (userStatus === 'inactive') {
    return (
      <CuentaInactiva
        handleVerifyStatus={handleVerifyStatus}
        isVerifying={isVerifying}
        statusLoading={statusLoading}
      />
    );
  }

  return (
    <>
      <style>{`
        .estado-cuenta-tabs .nav-tabs {
          border-bottom: none;
          margin-bottom: 1.5rem;
          gap: 0.5rem;
        }
        .estado-cuenta-tabs .nav-tabs .nav-link {
          border: 2px solid rgba(var(--bs-primary-rgb), 0.15);
          background: rgba(255, 255, 255, 0.95);
          color: #6b7280;
          border-radius: 12px;
          padding: 0.65rem 1.5rem;
          margin-right: 0;
          font-weight: 600;
          font-size: 0.875rem;
          position: relative;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          box-shadow:
            0 4px 8px -2px rgba(0, 0, 0, 0.08),
            0 2px 4px -1px rgba(0, 0, 0, 0.04);
          will-change: transform, box-shadow;
          text-transform: uppercase;
          letter-spacing: 0.5px;
          overflow: hidden;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link::after {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: linear-gradient(135deg, rgba(var(--bs-primary-rgb), 0.05) 0%, transparent 50%);
          opacity: 0;
          transition: opacity 0.3s ease;
          border-radius: 10px;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link:hover {
          background: rgba(var(--bs-primary-rgb), 0.05);
          color: var(--bs-primary);
          border-color: rgba(var(--bs-primary-rgb), 0.3);
          box-shadow:
            0 8px 16px -4px rgba(var(--bs-primary-rgb), 0.15),
            0 4px 8px -2px rgba(var(--bs-primary-rgb), 0.1);
          transform: translateY(-2px);
        }

        .estado-cuenta-tabs .nav-tabs .nav-link:hover::after {
          opacity: 1;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link.active {
          background: var(--bs-primary) !important;
          color: #ffffff !important;
          font-weight: 700;
          border: 2px solid var(--bs-primary);
          box-shadow:
            0 8px 20px -4px rgba(var(--bs-primary-rgb), 0.4),
            0 4px 12px -2px rgba(var(--bs-primary-rgb), 0.3),
            inset 0 1px 0 rgba(255, 255, 255, 0.2);
          transform: translateY(-3px);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
        }

        .estado-cuenta-tabs .nav-tabs .nav-link.active::after {
          background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
          opacity: 1;
        }

        .estado-cuenta-tabs .nav-tabs .nav-link:active {
          transform: translateY(-1px) scale(0.98);
          transition-duration: 0.1s;
          box-shadow:
            0 2px 8px -1px rgba(0, 0, 0, 0.15),
            0 1px 4px -1px rgba(0, 0, 0, 0.1);
        }
        .estado-cuenta-tabs .card,
        .estado-cuenta-tabs .border-0.shadow-sm.rounded {
          box-shadow: var(--bs-card-box-shadow) !important;
          border: 1px solid var(--bs-border-color) !important;
          border-radius: 16px !important;
          backdrop-filter: blur(10px);
          background: var(--bs-card-bg) !important;
        }

        @media (prefers-reduced-motion: reduce) {
          .estado-cuenta-tabs .nav-tabs .nav-link,
          .detail-content,
          .detail-expand-enter,
          .detail-expand-exit {
            transition: none !important;
            animation: none !important;
          }
        }

        .detail-expand-enter {
          opacity: 0;
          transform: translateY(-12px) scale(0.96);
          max-height: 0;
          overflow: hidden;
          transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
          will-change: transform, opacity, max-height;
        }

        .detail-expand-enter-active {
          opacity: 1;
          transform: translateY(0) scale(1);
          max-height: 2000px;
        }

        .detail-expand-exit {
          opacity: 1;
          transform: translateY(0) scale(1);
          max-height: 2000px;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          will-change: transform, opacity, max-height;
        }

        .detail-expand-exit-active {
          opacity: 0;
          transform: translateY(-8px) scale(0.98);
          max-height: 0;
          padding-top: 0 !important;
          padding-bottom: 0 !important;
          margin-top: 0 !important;
          margin-bottom: 0 !important;
        }

        .detail-content {
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          overflow: hidden;
          border-radius: 12px;
          contain: layout style paint;
        }

        .detail-content.animating {
          pointer-events: none;
          will-change: transform, opacity;
        }

        @keyframes smoothSlideIn {
          from {
            opacity: 0;
            transform: translateY(-16px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }

        @keyframes smoothSlideOut {
          from {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
          to {
            opacity: 0;
            transform: translateY(-12px) scale(0.96);
          }
        }

        .smooth-slide-in {
          animation: smoothSlideIn 0.3s cubic-bezier(0.34, 1.56, 0.64, 1) forwards;
        }

        .smooth-slide-out {
          animation: smoothSlideOut 0.25s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }

        html {
          scroll-behavior: smooth;
        }

        @media (prefers-reduced-motion: reduce) {
          html {
            scroll-behavior: auto;
          }
        }

        /* Enhanced scroll animation styles */
        .smooth-scroll-target {
          scroll-margin-top: 20px;
        }

        .scroll-indicator {
          transition: opacity 0.3s ease-out;
        }

        /* Ensure smooth scrolling performance */
        * {
          scroll-behavior: smooth;
        }

        @supports (scroll-behavior: smooth) {
          html {
            scroll-behavior: smooth;
          }
        }

        /* Professional Table Styling - Hover effects removed to prevent scroll bar issues */

        /* Enhanced card styling */
        .card.rounded-4 {
          border-radius: 1rem !important;
          overflow: hidden;
          backdrop-filter: blur(10px);
        }

        .card-header.bg-gradient {
          background: linear-gradient(135deg, var(--bs-dark) 0%, #2c3e50 100%) !important;
        }

        /* Smooth transitions for all interactive elements */
        .transition-all {
          transition: all 0.25s ease-in-out;
        }

        /* Badge improvements */
        .badge.rounded-pill {
          font-size: 0.75rem;
          font-weight: 600;
          letter-spacing: 0.025em;
          text-transform: uppercase;
          display: inline-flex;
          align-items: center;
          justify-content: center;
          min-height: 22px;
          line-height: 1;
          vertical-align: middle;
        }

        /* Money amount styling */
        .fw-bold.fs-6 {
          font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
          letter-spacing: 0.025em;
        }

        /* Disable animations for reduced motion preference */
        @media (prefers-reduced-motion: reduce) {
          .transition-all,
          .card,
          .btn {
            transition: none !important;
            animation: none !important;
          }
        }

        /* Mobile-Specific Responsive Enhancements */
        @media (max-width: 991.98px) {
          .detail-content {
            margin: calc(0.5rem + 0.2vw) 0;
            border-radius: calc(8px + 0.3vw) !important;
          }

          .card-body {
            padding: calc(0.75rem + 0.5vw) !important;
          }

          .btn {
            min-height: calc(40px + 0.5vw);
            min-width: calc(40px + 0.5vw);
          }

          .table-responsive {
            border-radius: calc(8px + 0.2vw);
          }

          /* Enhanced mobile card styling */
          .card.rounded-4 {
            border-radius: calc(12px + 0.2vw) !important;
            box-shadow: 0 4px 16px rgba(0,0,0,0.08), 0 2px 8px rgba(0,0,0,0.04) !important;
          }

          /* Mobile detail cards improvements */
          .detail-content .card {
            border-radius: calc(10px + 0.15vw) !important;
            margin-bottom: calc(0.5rem + 0.1vw) !important;
          }

          /* Mobile summary cards improvements */
          .row .col-4 .card {
            min-height: calc(90px + 1vw);
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
          }

          .row .col-4 .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.12), 0 3px 10px rgba(0,0,0,0.06) !important;
          }

          /* Ensure text doesn't overflow in mobile cards */
          .row .col-4 .card h6 {
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
          }

          /* Better spacing for mobile summary cards */
          .row .col-4 .card .card-body {
            padding: calc(0.5rem + 0.2vw) !important;
          }

          /* Mobile badge enhancements */
          .badge.rounded-pill {
            box-shadow: 0 2px 6px rgba(0,0,0,0.15) !important;
            border: 1px solid rgba(255,255,255,0.2) !important;
          }

          /* Mobile icon containers */
          .bg-primary.bg-opacity-15,
          .bg-success.bg-opacity-15,
          .bg-info.bg-opacity-15,
          .bg-danger.bg-opacity-15 {
            box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;
            border-width: 1.5px !important;
          }

          /* Perfect icon alignment */
          .rounded-circle {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
          }

          /* Enhanced pill alignment */
          .bg-white.bg-opacity-60,
          .bg-white.bg-opacity-40 {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            text-align: center !important;
          }
        }

        @media (max-width: 767.98px) {
          .detail-content {
            margin: calc(0.25rem + 0.3vw) 0;
            padding: calc(0.5rem + 0.3vw);
          }

          .card {
            margin-bottom: calc(0.75rem + 0.25vw) !important;
          }

          .row.g-2 {
            --bs-gutter-x: calc(0.5rem + 0.2vw);
            --bs-gutter-y: calc(0.5rem + 0.2vw);
          }

          .badge {
            font-size: calc(0.65rem + 0.1vw) !important;
            padding: calc(0.25rem + 0.1vw) calc(0.5rem + 0.15vw) !important;
          }
        }

        @media (max-width: 575.98px) {
          .container-fluid {
            padding-left: calc(0.5rem + 0.5vw) !important;
            padding-right: calc(0.5rem + 0.5vw) !important;
          }

          .card-body {
            padding: calc(0.75rem + 0.3vw) !important;
          }

          .detail-content .card-body {
            padding: calc(0.5rem + 0.25vw) !important;
          }

          .btn {
            min-height: calc(44px + 0.3vw);
            min-width: calc(44px + 0.3vw);
          }

          /* Extra small screen adjustments for summary cards */
          .row .col-4 .card h6 {
            font-size: calc(0.6rem + 0.1vw) !important;
            line-height: 1.1 !important;
            min-height: calc(2.2em) !important;
          }

          .row .col-4 .card h3 {
            font-size: calc(0.9rem + 0.2vw) !important;
          }

          .row .col-4 .card .card-body {
            padding: calc(0.4rem + 0.15vw) !important;
          }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
          .btn {
            min-height: 44px;
            min-width: 44px;
          }

          .card {
            margin-bottom: 1rem !important;
          }

          .detail-content {
            margin: 0.75rem 0;
          }

          /* Enhanced touch targets for mobile */
          .card-header {
            padding: 0.75rem 1rem !important;
          }

          .badge {
            min-height: 24px !important;
            padding: 0.25rem 0.75rem !important;
          }

          /* Better visual feedback for touch - only for buttons */

          .btn:active {
            transform: scale(0.95);
          }

          /* Improved spacing for touch interfaces */
          .row.g-2 {
            --bs-gutter-x: 0.75rem;
            --bs-gutter-y: 0.75rem;
          }

          .detail-content .row.g-2 {
            --bs-gutter-x: 0.5rem;
            --bs-gutter-y: 0.5rem;
          }
        }

        /* High DPI displays */
        @media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
          .card {
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08), 0 1px 4px rgba(0, 0, 0, 0.04) !important;
          }

          .btn {
            box-shadow: 0 2px 6px rgba(var(--bs-primary-rgb), 0.25) !important;
          }
        }

        /* Landscape mobile optimization */
        @media (max-height: 500px) and (orientation: landscape) {
          .card-body {
            padding: calc(0.5rem + 0.2vw) !important;
          }

          .detail-content {
            margin: calc(0.25rem + 0.1vw) 0;
          }

          .row.g-2 {
            --bs-gutter-y: calc(0.25rem + 0.1vw);
          }
        }

        /* Ensure minimum touch targets */
        .min-width-0 {
          min-width: 0 !important;
        }

        .text-truncate {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        /* Enhanced Company Name Display */
        .company-name-container,
        .company-name-mobile-container {
          position: relative;
          cursor: help;
          transition: all 0.2s ease-in-out;
        }

        .company-name-container:hover,
        .company-name-mobile-container:hover {
          transform: translateY(-1px);
        }

        /* Improved table layout for company names */
        .table td:first-child {
          position: relative;
          vertical-align: top;
        }

        /* Better spacing for desktop table */
        @media (min-width: 992px) {
          .table td:first-child {
            min-width: 280px;
            max-width: 400px;
            width: 35%;
          }

          .table td:not(:first-child):not(:last-child) {
            min-width: 120px;
            width: auto;
          }

          .table td:last-child {
            width: 80px;
            min-width: 80px;
          }

          .company-name-container h6 {
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
          }
        }

        /* Mobile-specific company name improvements */
        @media (max-width: 991.98px) {
          .company-name-mobile-container {
            margin-bottom: calc(0.5rem + 0.1vw);
          }

          .company-name-mobile-container h6 {
            word-wrap: break-word;
            overflow-wrap: break-word;
            hyphens: auto;
            line-height: 1.3;
          }

          .card-body {
            padding: calc(1rem + 0.3vw) !important;
          }
        }

        /* Tooltip-like behavior for long company names */
        .company-name-container[title]:hover::after,
        .company-name-mobile-container[title]:hover::after {
          content: attr(title);
          position: absolute;
          bottom: 100%;
          left: 0;
          right: 0;
          background: rgba(0, 0, 0, 0.9);
          color: white;
          padding: 0.5rem 0.75rem;
          border-radius: 6px;
          font-size: 0.85rem;
          font-weight: 500;
          line-height: 1.3;
          z-index: 1000;
          white-space: normal;
          word-wrap: break-word;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          opacity: 0;
          transform: translateY(4px);
          animation: tooltipFadeIn 0.2s ease-out forwards;
          pointer-events: none;
        }

        @keyframes tooltipFadeIn {
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        /* Enhanced spacing for eye toggle button */
        .table td:last-child .d-flex {
          min-height: 60px;
          align-items: center;
        }

        /* Better visual hierarchy */
        .company-name-container h6,
        .company-name-mobile-container h6 {
          font-weight: 600;
          letter-spacing: -0.01em;
          color: #1a1a1a;
        }

        .company-name-container small,
        .company-name-mobile-container small {
          color: #6c757d;
          font-weight: 500;
          letter-spacing: 0.01em;
        }

        /* Improved icon container */
        .bg-primary.bg-opacity-10 {
          transition: all 0.2s ease-in-out;
          border: 1px solid rgba(var(--bs-primary-rgb), 0.2) !important;
        }

        .bg-primary.bg-opacity-10:hover {
          background-color: rgba(var(--bs-primary-rgb), 0.15) !important;
          border-color: rgba(var(--bs-primary-rgb), 0.3) !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(var(--bs-primary-rgb), 0.15);
        }

        /* Compact Detail Sections */
        .detail-content {
          padding: 0 !important;
        }

        .detail-content .table-sm {
          font-size: 0.8rem;
          margin-bottom: 0;
        }

        .detail-content .table-sm td,
        .detail-content .table-sm th {
          padding: 0.4rem 0.6rem;
          vertical-align: middle;
          line-height: 1.2;
        }

        .detail-content .table-sm thead th {
          padding: 0.5rem 0.6rem;
          font-size: 0.75rem;
          font-weight: 600;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        /* Compact mobile detail cards */
        .detail-content .card-body {
          padding: 0.5rem !important;
          min-height: auto !important;
        }

        .detail-content .row.g-1 {
          --bs-gutter-x: 0.25rem;
          --bs-gutter-y: 0.25rem;
        }

        .detail-content .badge {
          font-size: 0.65rem !important;
          padding: 0.15rem 0.4rem !important;
          line-height: 1.1 !important;
        }

        /* Responsive compact adjustments */
        @media (max-width: 991.98px) {
          .detail-content {
            margin: calc(0.25rem + 0.1vw) 0;
            border-radius: calc(8px + 0.2vw) !important;
          }

          .detail-content .p-2 {
            padding: calc(0.5rem + 0.1vw) !important;
          }

          .detail-content .card-body {
            padding: calc(0.4rem + 0.1vw) !important;
          }

          .detail-content .row.g-1 {
            --bs-gutter-x: calc(0.2rem + 0.05vw);
            --bs-gutter-y: calc(0.2rem + 0.05vw);
          }
        }

        @media (max-width: 767.98px) {
          .detail-content .p-2 {
            padding: calc(0.4rem + 0.1vw) !important;
          }

          .detail-content .card-body {
            padding: calc(0.35rem + 0.1vw) !important;
          }

          .detail-content h6 {
            font-size: 0.8rem !important;
            margin-bottom: 0.5rem !important;
          }

          .detail-content .badge {
            font-size: 0.6rem !important;
            padding: 0.1rem 0.3rem !important;
          }
        }

        @media (max-width: 575.98px) {
          .detail-content .p-2 {
            padding: calc(0.35rem + 0.1vw) !important;
          }

          .detail-content .card-body {
            padding: calc(0.3rem + 0.1vw) !important;
          }

          .detail-content .row.g-1 {
            --bs-gutter-x: 0.15rem;
            --bs-gutter-y: 0.15rem;
          }
        }

        /* Enhanced table density */
        .table-sm tbody tr {
          height: auto;
          min-height: 2rem;
        }

        .table-sm .badge {
          font-size: 0.65rem;
          padding: 0.15rem 0.4rem;
          line-height: 1.1;
        }

        /* Compact spacing for detail headers */
        .detail-content h6 {
          margin-bottom: 0.5rem !important;
          font-size: 0.85rem !important;
          line-height: 1.2 !important;
        }

        /* Optimize card spacing in details */
        .detail-content .card {
          margin-bottom: 0.25rem !important;
        }

        .detail-content .card:last-child {
          margin-bottom: 0 !important;
        }

        /* Tighter typography in details */
        .detail-content .small {
          font-size: 0.7rem !important;
          line-height: 1.1 !important;
          margin-bottom: 0.15rem !important;
        }

        .detail-content .fw-medium,
        .detail-content .fw-bold {
          line-height: 1.2 !important;
        }

        /* Enhanced mobile typography and contrast */
        @media (max-width: 767.98px) {
          .text-muted {
            color: #5a6169 !important;
          }

          .fw-bold {
            font-weight: 700 !important;
          }

          .fw-semibold {
            font-weight: 600 !important;
          }

          /* Better contrast for monetary amounts */
          .text-danger {
            color: #dc2626 !important;
          }

          .text-success {
            color: #059669 !important;
          }

          /* Enhanced card backgrounds for better readability */
          .card-header {
            backdrop-filter: blur(8px) !important;
          }

          .card-body {
            backdrop-filter: blur(4px) !important;
          }

          /* Improved shadow depth for mobile */
          .shadow-sm {
            box-shadow: 0 2px 8px rgba(0,0,0,0.12), 0 1px 4px rgba(0,0,0,0.08) !important;
          }

          .shadow {
            box-shadow: 0 4px 16px rgba(0,0,0,0.15), 0 2px 8px rgba(0,0,0,0.1) !important;
          }
        }

        /* Dark mode considerations for mobile */
        @media (prefers-color-scheme: dark) and (max-width: 767.98px) {
          .card {
            background: rgba(255,255,255,0.95) !important;
            border-color: rgba(0,0,0,0.1) !important;
          }

          .detail-content {
            background: linear-gradient(135deg, rgba(248, 249, 250, 0.98) 0%, rgba(233, 236, 239, 0.95) 100%) !important;
          }
        }

        /* Desktop-specific compact layout optimizations */
        @media (min-width: 992px) {
          .estado-cuenta-tabs .card-body {
            padding: 1rem !important;
          }

          .detail-content {
            max-width: 95% !important;
            margin-left: auto !important;
            margin-right: auto !important;
          }

          .tab-pane {
            padding: 0.5rem !important;
          }
        }

        @media (min-width: 1200px) {
          .estado-cuenta-tabs .card-body {
            padding: 0.75rem !important;
          }

          .detail-content {
            max-width: 98% !important;
          }

          .tab-pane {
            padding: 0.25rem !important;
          }
        }

      `}</style>

        {/* Estado de Cuenta Tabbed Card */}
        <div className="card border-0 shadow-sm rounded-3 overflow-hidden">
          <div className="card-body estado-cuenta-tabs p-3 p-md-4 p-lg-2 p-xl-1">
            <div className="mb-3 mb-md-4 fw-bold d-flex align-items-center"
                 style={{fontSize: 'calc(1.2rem + 0.3vw)'}}>
              <FA6.FaMoneyBillWave className="me-2 me-md-3 text-primary"
                                   style={{fontSize: 'calc(1.1rem + 0.2vw)'}} />
              <span>Estado de Cuenta</span>
            </div>
            <ul className="nav nav-tabs mb-3 mb-md-4" role="tablist">
              {Object.values(tabConfigs).map((config) => (
                <li key={config.id} className="nav-item flex-fill flex-sm-fill-0">
                  <a
                    className={`nav-link text-center ${activeTab === config.id ? 'active' : ''}`}
                    data-bs-toggle="tab"
                    href={`#${config.id}`}
                    role="tab"
                    style={{
                      fontSize: 'calc(0.8rem + 0.1vw)',
                      padding: 'calc(0.5rem + 0.2vw) calc(0.75rem + 0.3vw)',
                      minHeight: 'calc(40px + 0.5vw)'
                    }}
                    onClick={(e) => {
                      e.preventDefault();
                      setActiveTab(config.id);
                    }}
                  >
                    <span>{config.label}</span>
                  </a>
                </li>
              ))}
            </ul>
            <div className="tab-content">
              {renderTabContent(activeTab)}
            </div>
          </div>
        </div>

    </>
  );
};

// Define page metadata
EstadoDeCuenta.pageMetadata = {
  title: 'Movimientos',
  description: 'Movimientos de Cuenta',
  icon: <FA6.FaMoneyBillTransfer className="fs-6" />,
  showInMenu: true,
  menuOrder: 2,
  path: '/main/estadodecuenta',
  requiresAuth: true,
  section: 'PERSONA',
  parent: 'Persona',
  permissions: {
    requiredPermission: PERMISSIONS.VIEW,
    resourceId: 'estadodecuenta'
  }
};

export default EstadoDeCuenta;
